package com.example.my_music_001.ui.components

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.graphics.RenderEffect
import android.graphics.Shader
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.renderscript.Allocation
import android.renderscript.Element
import android.renderscript.RenderScript
import android.renderscript.ScriptIntrinsicBlur
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
// 导入MainActivity类
import com.example.my_music_001.MainActivity
// 删除对私有变量的导入
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.forEachGesture
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import android.util.Log
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.clipRect
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.zIndex
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalConfiguration
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest

import kotlin.math.roundToInt

// RenderScript模糊工具类
// 将类声明为public，以便MainActivity可以访问
public class BlurEffect {
    companion object {
        // 缓存已处理过的模糊图像，避免重复计算
        private val blurCache = mutableMapOf<String, Bitmap>()
        
        fun applyBlur(context: Context, originalBitmap: Bitmap, radius: Float): Bitmap {
            // 创建缓存键
            val cacheKey = "${originalBitmap.hashCode()}_${radius}"
            
            // 检查缓存中是否已有此图像的模糊版本
            blurCache[cacheKey]?.let {
                if (!it.isRecycled) {
                    return it
                }
            }
            
            // 放大图像以获得更好的模糊效果
            val scaleFactor = 0.07f  // 放大2倍
            val width = (originalBitmap.width * scaleFactor).toInt()
            val height = (originalBitmap.height * scaleFactor).toInt()
            
            // 创建放大版本的位图
            val inputBitmap = Bitmap.createScaledBitmap(originalBitmap, width, height, true)
            
            val rs = RenderScript.create(context)
            val input = Allocation.createFromBitmap(rs, inputBitmap)
            val output = Allocation.createTyped(rs, input.type)
            
            // 使用单次高效模糊
            val script = ScriptIntrinsicBlur.create(rs, Element.U8_4(rs))
            script.setRadius(radius.coerceIn(0f, 25f))
            script.setInput(input)
            script.forEach(output)
            
            // 直接在放大的尺寸上创建模糊结果
            val blurredBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            output.copyTo(blurredBitmap)
            
            // 缩小回原始尺寸，保持模糊效果的质量
            val resultBitmap = Bitmap.createScaledBitmap(
                blurredBitmap,
                originalBitmap.width,
                originalBitmap.height,
                true
            )
            
            // 清理资源
            input.destroy()
            output.destroy()
            rs.destroy()
            inputBitmap.recycle()
            blurredBitmap.recycle()
            
            // 缓存结果
            blurCache[cacheKey] = resultBitmap
            
            // 限制缓存大小
            if (blurCache.size > 5) {
                blurCache.entries.firstOrNull()?.let {
                    blurCache.remove(it.key)
                }
            }
            
            return resultBitmap
        }

        // 缓存裁剪区域的键
        private val regionCache = mutableMapOf<String, Bitmap>()
        
        fun createBlurredRegion(
            context: Context,
            originalBitmap: Bitmap,
            x: Int,
            y: Int,
            width: Int,
            height: Int,
            radius: Float = 20f
        ): Bitmap? {
            try {
                // 创建缓存键
                val cacheKey = "${originalBitmap.hashCode()}_${x}_${y}_${width}_${height}_${radius}"
                
                // 检查缓存
                regionCache[cacheKey]?.let {
                    if (!it.isRecycled) {
                        return it
                    }
                }
                
                // 确保裁剪区域在原图范围内
                val safeX = x.coerceIn(0, originalBitmap.width - 1)
                val safeY = y.coerceIn(0, originalBitmap.height - 1)
                val safeWidth = width.coerceIn(1, originalBitmap.width - safeX)
                val safeHeight = height.coerceIn(1, originalBitmap.height - safeY)
                
                // 直接在原图上应用模糊，避免额外的裁剪操作
                val result = if (safeX == 0 && safeY == 0 && 
                    safeWidth == originalBitmap.width && safeHeight == originalBitmap.height) {
                    // 整图模糊
                    applyBlur(context, originalBitmap, radius)
                } else {
                    // 裁剪指定区域
                    val croppedBitmap = Bitmap.createBitmap(
                        originalBitmap,
                        safeX,
                        safeY,
                        safeWidth,
                        safeHeight
                    )
                    
                    // 应用模糊效果
                    val blurred = applyBlur(context, croppedBitmap, radius)
                    
                    // 如果是新创建的裁剪位图，则回收
                    if (croppedBitmap != originalBitmap) {
                        croppedBitmap.recycle()
                    }
                    
                    blurred
                }
                
                // 缓存结果
                regionCache[cacheKey] = result
                
                // 限制缓存大小
                if (regionCache.size > 3) {
                    regionCache.entries.firstOrNull()?.let {
                        regionCache.remove(it.key)
                    }
                }
                
                return result
            } catch (e: Exception) {
                Log.e("BlurEffect", "创建模糊区域失败: ${e.message}")
                return null
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundBitmap by remember { mutableStateOf<Bitmap?>(null) }
    var squareOffset by remember { mutableStateOf(Offset(200f, 400f)) }
    var containerSize by remember { mutableStateOf(Size.Zero) }

    val context = LocalContext.current
    val density = LocalDensity.current

    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            try {
                val inputStream = context.contentResolver.openInputStream(it)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                backgroundBitmap = bitmap
                inputStream?.close()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)  // 将白色背景改为黑色
            .onGloballyPositioned { coordinates ->
                containerSize = coordinates.size.toSize()
            }
    ) {
        // 背景图片
        var backgroundImageBitmap by remember { mutableStateOf<ImageBitmap?>(null) }

        backgroundBitmap?.let { bitmap ->
            val imageBitmap = bitmap.asImageBitmap()
            backgroundImageBitmap = imageBitmap

            Image(
                bitmap = imageBitmap,
                contentDescription = "Background",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }

        // 导入按钮
        Button(
            onClick = {
                imagePickerLauncher.launch("image/*")
            },
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(16.dp)
                .zIndex(3f)
        ) {
            Text("导入图片")
        }

        // 使用RenderScript的iOS风格毛玻璃效果的可拖动方形
        RenderScriptGlassBox(
            offset = squareOffset,
            onOffsetChange = { newOffset ->
                val squareSize = with(density) { 120.dp.toPx() }
                val maxX = containerSize.width - squareSize
                val maxY = containerSize.height - squareSize

                squareOffset = Offset(
                    x = newOffset.x.coerceIn(0f, maxX),
                    y = newOffset.y.coerceIn(0f, maxY)
                )
            },
            backgroundBitmap = backgroundBitmap,
            containerSize = containerSize,
            isNightMode = false, // 默认非夜间模式
            modifier = Modifier
                .size(120.dp)
                .zIndex(2f)
        )
    }
}

@Composable
fun RenderScriptGlassBox(
    offset: Offset,
    onOffsetChange: (Offset) -> Unit,
    backgroundBitmap: Bitmap?,
    containerSize: Size,
    isNightMode: Boolean, // 添加夜间模式参数
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    
    // 使用状态记录位置和背景
    var boxPosition by remember { mutableStateOf(offset) }
    var boxSize by remember { mutableStateOf(Size.Zero) }
    var lastBackgroundHash by remember { mutableStateOf(0) }
    
    // 缓存模糊后的bitmap
    var blurredBitmap by remember { mutableStateOf<Bitmap?>(null) }

    // 当位置或背景图片变化时立即重新生成模糊效果
    LaunchedEffect(boxPosition, backgroundBitmap?.hashCode()) {
        // 检查背景是否发生变化
        val currentHash = backgroundBitmap?.hashCode() ?: 0
        val backgroundChanged = currentHash != lastBackgroundHash
        
        // 更新哈希值
        lastBackgroundHash = currentHash
        
        backgroundBitmap?.let { bitmap ->
            // 获取组件尺寸
            val compSize = boxSize.takeIf { it.width > 0 && it.height > 0 } 
                ?: Size(with(density) { 200.dp.toPx() }, with(density) { 200.dp.toPx() })
            
            // 计算背景图片的缩放比例
            val scaleX = containerSize.width / bitmap.width
            val scaleY = containerSize.height / bitmap.height
            val scale = maxOf(scaleX, scaleY)
            
            // 计算在原图中的坐标
            val srcX = (boxPosition.x / scale).toInt()
            val srcY = (boxPosition.y / scale).toInt()
            val srcWidth = (compSize.width / scale).toInt()
            val srcHeight = (compSize.height / scale).toInt()
            
            try {
                // 直接使用优化后的方法创建模糊区域
                val newBlurred = BlurEffect.createBlurredRegion(
                    context = context,
                    originalBitmap = bitmap,
                    x = srcX,
                    y = srcY,
                    width = srcWidth,
                    height = srcHeight,
                    radius = 25f
                )
                
                // 更新状态
                blurredBitmap = newBlurred
            } catch (e: Exception) {
                Log.e("RenderScriptGlassBox", "模糊处理错误: ${e.message}")
            }
        }
    }

    // 使用固定的透明度值，不再从MainActivity获取
    // 增加透明度值，使效果更明显
    val glassAlpha = 0.40f
    
    // 定义局部函数，处理毛玻璃效果的绘制
    fun drawGlassEffect(drawScope: DrawScope, bitmap: Bitmap?) {
        with(drawScope) {
            // 根据夜间模式选择颜色
            val glassColor = if (isNightMode) Color.Black else Color.White
            
            // 如果没有模糊背景，绘制一个默认的半透明背景
            if (bitmap == null) {
                // 绘制一个半透明背景，模拟毛玻璃效果
                drawRect(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            glassColor.copy(alpha = glassAlpha + 0.05f),
                            glassColor.copy(alpha = glassAlpha)
                        )
                    ),
                    size = size
                )
            } else {
                // 绘制RenderScript模糊背景
                val imageBitmap = bitmap.asImageBitmap()

                // 绘制模糊背景
                drawImage(
                    image = imageBitmap,
                    dstOffset = IntOffset.Zero,
                    dstSize = IntSize(size.width.toInt(), size.height.toInt()),
                    filterQuality = FilterQuality.High
                )
                
                // 在模糊背景上添加叠加层，根据夜间模式选择颜色
                drawRect(
                    color = glassColor.copy(alpha = 0.6f),
                    size = size
                )
            }
            
            // 添加整体半透明叠加层，增强玻璃感
            drawRect(
                color = glassColor.copy(alpha = glassAlpha),
                size = size
            )
            
            // 删除顶部高光、边缘阴影和底部阴影，只保留主要的半透明背景
        }
    }

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(28.dp)) // 使用更大的圆角，匹配设置弹窗
            // 移除阴影，因为它已经是弹窗背景
            .onGloballyPositioned { coordinates ->
                // 更新组件的实际尺寸
                boxSize = coordinates.size.toSize()
            }
            .drawWithContent {
                // 直接调用局部函数
                drawGlassEffect(this, blurredBitmap)
            }
    ) {
        // 移除标签，使其作为背景层时更加干净
    }
    
    // 更新位置
    LaunchedEffect(offset) {
        boxPosition = offset
    }
}


// 如果设备支持，可以使用系统级别的模糊效果
@Composable
fun SystemBlurGlassBox(
    modifier: Modifier = Modifier,
    isNightMode: Boolean = false, // 添加夜间模式参数
    content: @Composable BoxScope.() -> Unit = {}
) {
    // 根据夜间模式选择颜色
    val glassColor = if (isNightMode) Color.Black else Color.White
    
    Box(
        modifier = modifier
            .then(
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    Modifier.blur(radius = 20.dp)
                } else {
                    Modifier
                }
            )
            .background(
                color = glassColor.copy(alpha = 0.2f),
                shape = RoundedCornerShape(16.dp)
            )
            .clip(RoundedCornerShape(16.dp))
    ) {
        content()
    }
}

/**
 * 局部模糊组件 - 专门为歌曲选项弹窗设计
 * 只对弹窗区域内的背景进行模糊处理
 */
@Composable
fun LocalBlurBox(
    modifier: Modifier = Modifier,
    backgroundBitmap: Bitmap? = null,
    dialogOffset: Offset = Offset.Zero,
    dialogSize: Size = Size.Zero,
    containerSize: Size = Size.Zero,
    blurRadius: Float = 25f,
    isNightMode: Boolean = false,
    cornerRadius: androidx.compose.ui.unit.Dp = 28.dp,
    content: @Composable BoxScope.() -> Unit = {}
) {
    val context = LocalContext.current

    // 缓存模糊后的bitmap
    var localBlurredBitmap by remember { mutableStateOf<Bitmap?>(null) }
    var lastBackgroundHash by remember { mutableStateOf(0) }

    // 当背景图片发生变化时，重新生成局部模糊效果
    LaunchedEffect(backgroundBitmap) {
        if (backgroundBitmap != null) {
            try {
                val currentHash = backgroundBitmap.hashCode()

                // 只有当背景图片真正改变时才重新生成模糊效果
                if (currentHash != lastBackgroundHash) {
                    lastBackgroundHash = currentHash

                    withContext(Dispatchers.IO) {
                        // 简化版本：直接对整个背景图片应用模糊，然后在绘制时裁剪
                        val blurred = BlurEffect.applyBlur(context, backgroundBitmap, blurRadius)

                        // 回收之前的模糊bitmap
                        localBlurredBitmap?.recycle()

                        // 更新模糊bitmap
                        localBlurredBitmap = blurred
                    }
                }
            } catch (e: Exception) {
                Log.e("LocalBlurBox", "生成局部模糊效果失败: ${e.message}", e)
            }
        }
    }

    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            localBlurredBitmap?.recycle()
        }
    }

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(cornerRadius))
            .drawWithContent {
                // 绘制局部模糊背景
                localBlurredBitmap?.let { bitmap ->
                    val imageBitmap = bitmap.asImageBitmap()

                    // 计算背景图片在弹窗中的显示区域
                    if (containerSize.width > 0 && containerSize.height > 0) {
                        val scaleX = size.width / containerSize.width
                        val scaleY = size.height / containerSize.height
                        val offsetX = -dialogOffset.x * scaleX
                        val offsetY = -dialogOffset.y * scaleY

                        drawImage(
                            image = imageBitmap,
                            dstOffset = IntOffset(offsetX.toInt(), offsetY.toInt()),
                            dstSize = IntSize(
                                (imageBitmap.width * scaleX).toInt(),
                                (imageBitmap.height * scaleY).toInt()
                            ),
                            filterQuality = FilterQuality.High
                        )
                    } else {
                        // 降级方案：直接填充整个区域
                        drawImage(
                            image = imageBitmap,
                            dstOffset = IntOffset.Zero,
                            dstSize = IntSize(size.width.toInt(), size.height.toInt()),
                            filterQuality = FilterQuality.High
                        )
                    }
                }

                // 根据夜间模式添加叠加层
                val glassColor = if (isNightMode) Color.White else Color.Black
                drawRect(
                    color = glassColor.copy(alpha = 0.3f),
                    size = size
                )

                // 绘制内容
                drawContent()
            }
    ) {
        content()
    }
}

/**
 * 实时模糊弹窗组件 - 基于Coil图片加载和实时背景模糊
 * 支持拖拽移动和点击关闭
 */
@Composable
fun RealTimeBlurDialog(
    backgroundImageUri: Uri? = null,
    backgroundImagePath: String? = null,
    onDismiss: () -> Unit,
    onOffsetChange: (Offset) -> Unit,
    modifier: Modifier = Modifier,
    dialogWidth: androidx.compose.ui.unit.Dp = 320.dp,
    dialogHeight: androidx.compose.ui.unit.Dp = 145.dp,
    blurRadius: androidx.compose.ui.unit.Dp = 80.dp,
    cornerRadius: androidx.compose.ui.unit.Dp = 20.dp,
    overlayAlpha: Float = 0.15f,
    content: @Composable BoxScope.() -> Unit = {}
) {
    // 拖动偏移状态
    var offset by remember { mutableStateOf(Offset.Zero) }

    // 获取屏幕尺寸
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeightPx = with(density) { configuration.screenHeightDp.dp.toPx() }

    // 通知父组件偏移变化
    LaunchedEffect(offset) {
        onOffsetChange(offset)
    }

    // 使用Box覆盖整个屏幕
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0f))
            .pointerInput(Unit) {
                // 点击空白区域关闭弹窗
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        // 毛玻璃效果的弹窗
        // 外部容器，处理拖拽和边框
        Box(
            modifier = Modifier
                .width(dialogWidth)
                .height(dialogHeight)
                .offset { IntOffset(offset.x.roundToInt(), offset.y.roundToInt()) }
                .align(Alignment.Center)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
        ) {
            // 实时背景模糊层 - 重新渲染背景内容
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(cornerRadius))
            ) {
                // 背景图片 - 保持全屏尺寸，根据弹窗位置进行偏移
                val imageData = backgroundImageUri ?: backgroundImagePath
                imageData?.let { data ->
                    Image(
                        painter = rememberAsyncImagePainter(
                            ImageRequest.Builder(LocalContext.current)
                                .data(data)
                                .build()
                        ),
                        contentDescription = "实时模糊背景",
                        colorFilter = ColorFilter.colorMatrix(
                            colorMatrix = ColorMatrix(
                                floatArrayOf(
                                    1.4f, 0f, 0f, 0f, -0.4f,  // 红色通道：保持对比度，但降低白色
                                    0f, 1.4f, 0f, 0f, -0.4f,  // 绿色通道：保持对比度，但降低白色
                                    0f, 0f, 1.4f, 0f, -0.4f,  // 蓝色通道：保持对比度，但降低白色
                                    0f, 0f, 0f, 1f, 0f       // 透明度通道
                                )
                            )
                        ),
                        modifier = Modifier
                            .layout { measurable, constraints ->
                                // 使用真实的屏幕尺寸
                                val screenWidth = screenWidthPx.roundToInt()
                                val screenHeight = screenHeightPx.roundToInt()
                                val placeable = measurable.measure(
                                    constraints.copy(
                                        minWidth = screenWidth,
                                        maxWidth = screenWidth,
                                        minHeight = screenHeight,
                                        maxHeight = screenHeight
                                    )
                                )
                                layout(constraints.maxWidth, constraints.maxHeight) {
                                    placeable.place(
                                        -offset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2,
                                        -offset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2
                                    )
                                }
                            }
                            .blur(blurRadius), // 模糊效果
                        contentScale = ContentScale.Crop
                    )
                }

                // 半透明白色背景层（最上层）
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.White.copy(alpha = overlayAlpha))
                        .clip(RoundedCornerShape(cornerRadius))
                )
            }

            // 弹窗内容
            content()
        }
    }
}